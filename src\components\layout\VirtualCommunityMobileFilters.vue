<template>
  <div class="mobile-filters-container">
    <!-- Tab Navigation - Hidden on mobile -->
    <!-- <div class="mobile-tabs">
      <q-btn-toggle
        v-model="selectedTab"
        toggle-color="primary"
        :options="tabOptions"
        @update:model-value="handleTabChange"
        class="tab-toggle"
        dense
        unelevated
      />
    </div> -->

    <!-- Filter Actions -->
    <div class="filter-actions">
      <!-- AI Filter Trigger -->
      <q-btn
        flat
        dense
        round
        icon="smart_toy"
        color="primary"
        @click="openAIFilter"
        class="ai-filter-btn"
      >
        <q-tooltip>AI Search</q-tooltip>
      </q-btn>

      <!-- Sort Options -->
      <q-btn
        flat
        dense
        round
        icon="sort"
        @click="openSortMenu"
        class="sort-btn"
      >
        <q-tooltip>Sort</q-tooltip>
        <q-menu>
          <q-list style="min-width: 150px">
            <q-item
              v-for="option in sortOptions"
              :key="option.value"
              clickable
              v-close-popup
              @click="handleSortChange(option.value)"
              :active="currentSort === option.value"
            >
              <q-item-section avatar>
                <q-icon :name="option.icon" />
              </q-item-section>
              <q-item-section>{{ option.label }}</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>

      <!-- Filter Menu -->
      <q-btn
        flat
        dense
        round
        icon="filter_list"
        @click="openFilterMenu"
        class="filter-btn"
      >
        <q-badge
          v-if="activeFiltersCount > 0"
          color="red"
          floating
          rounded
          :label="activeFiltersCount"
        />
        <q-tooltip>Filters</q-tooltip>
      </q-btn>
    </div>

    <!-- AI Filter Dialog -->
    <q-dialog v-model="showAIFilter" position="top">
      <q-card class="ai-filter-card">
        <q-card-section class="ai-filter-header">
          <div class="text-h6">AI Search</div>
          <q-btn flat round dense icon="close" v-close-popup />
        </q-card-section>
        
        <q-card-section>
          <q-input
            v-model="aiQuery"
            outlined
            placeholder="Ask AI to find specific content..."
            @keyup.enter="executeAISearch"
            autofocus
          >
            <template v-slot:prepend>
              <q-icon name="smart_toy" color="primary" />
            </template>
            <template v-slot:append>
              <q-btn
                flat
                round
                dense
                icon="send"
                color="primary"
                @click="executeAISearch"
                :disable="!aiQuery.trim()"
              />
            </template>
          </q-input>
          
          <!-- Quick AI Suggestions -->
          <div class="ai-suggestions">
            <q-chip
              v-for="suggestion in aiSuggestions"
              :key="suggestion"
              clickable
              color="primary"
              outline
              :label="suggestion"
              @click="selectAISuggestion(suggestion)"
              class="suggestion-chip"
            />
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>

    <!-- Filter Menu Dialog -->
    <q-dialog v-model="showFilterMenu" position="bottom">
      <q-card class="filter-menu-card">
        <q-card-section class="filter-header">
          <div class="text-h6">Filters</div>
          <q-btn flat round dense icon="close" v-close-popup />
        </q-card-section>
        
        <q-card-section>
          <!-- Category Filters -->
          <div class="filter-group">
            <div class="filter-group-title">Categories</div>
            <q-option-group
              v-model="selectedCategories"
              :options="categoryOptions"
              type="checkbox"
              inline
              @update:model-value="handleFilterChange"
            />
          </div>

          <!-- Date Range -->
          <div class="filter-group">
            <div class="filter-group-title">Date Range</div>
            <q-select
              v-model="selectedDateRange"
              :options="dateRangeOptions"
              outlined
              dense
              @update:model-value="handleFilterChange"
            />
          </div>

          <!-- Profile Type (for profiles tab) -->
          <div v-if="activeTab === 'profiles'" class="filter-group">
            <div class="filter-group-title">Profile Type</div>
            <q-option-group
              v-model="selectedProfileTypes"
              :options="profileTypeOptions"
              type="checkbox"
              inline
              @update:model-value="handleFilterChange"
            />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat label="Clear All" @click="clearAllFilters" />
          <q-btn unelevated color="primary" label="Apply" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// Props
interface Props {
  activeTab: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'filter-change': [filters: any]
  'tab-change': [tab: string]
  'ai-search': [query: string]
}>()

// State
const selectedTab = ref(props.activeTab)
const showAIFilter = ref(false)
const showFilterMenu = ref(false)
const aiQuery = ref('')
const currentSort = ref('newest')
const selectedCategories = ref([])
const selectedDateRange = ref('all')
const selectedProfileTypes = ref([])

// Options
const tabOptions = [
  { label: 'Feed', value: 'feed' },
  { label: 'Profiles', value: 'profiles' },
  { label: 'Events', value: 'events' },
  { label: 'Blog', value: 'blog' },
  { label: 'Market', value: 'marketplace' }
]

const sortOptions = [
  { label: 'Newest First', value: 'newest', icon: 'schedule' },
  { label: 'Oldest First', value: 'oldest', icon: 'history' },
  { label: 'Most Popular', value: 'popular', icon: 'trending_up' },
  { label: 'Most Relevant', value: 'relevant', icon: 'star' }
]

const categoryOptions = [
  { label: 'General', value: 'general' },
  { label: 'Technology', value: 'technology' },
  { label: 'Business', value: 'business' },
  { label: 'Education', value: 'education' },
  { label: 'Innovation', value: 'innovation' }
]

const dateRangeOptions = [
  { label: 'All Time', value: 'all' },
  { label: 'Today', value: 'today' },
  { label: 'This Week', value: 'week' },
  { label: 'This Month', value: 'month' },
  { label: 'This Year', value: 'year' }
]

const profileTypeOptions = [
  { label: 'Innovators', value: 'innovator' },
  { label: 'Students', value: 'student' },
  { label: 'Mentors', value: 'mentor' },
  { label: 'Investors', value: 'investor' }
]

const aiSuggestions = [
  'Show trending posts',
  'Find mentors in tech',
  'Upcoming events',
  'Latest innovations',
  'Popular discussions'
]

// Computed
const activeFiltersCount = computed(() => {
  let count = 0
  if (selectedCategories.value.length > 0) count++
  if (selectedDateRange.value !== 'all') count++
  if (selectedProfileTypes.value.length > 0) count++
  return count
})

// Methods
function handleTabChange(tab: string) {
  emit('tab-change', tab)
}

function openAIFilter() {
  showAIFilter.value = true
}

function openSortMenu() {
  // Menu is handled by q-menu
}

function openFilterMenu() {
  showFilterMenu.value = true
}

function executeAISearch() {
  if (aiQuery.value.trim()) {
    emit('ai-search', aiQuery.value.trim())
    showAIFilter.value = false
    aiQuery.value = ''
  }
}

function selectAISuggestion(suggestion: string) {
  aiQuery.value = suggestion
  executeAISearch()
}

function handleSortChange(sortValue: string) {
  currentSort.value = sortValue
  handleFilterChange()
}

function handleFilterChange() {
  const filters = {
    sort: currentSort.value,
    categories: selectedCategories.value,
    dateRange: selectedDateRange.value,
    profileTypes: selectedProfileTypes.value,
    tab: selectedTab.value
  }
  emit('filter-change', filters)
}

function clearAllFilters() {
  selectedCategories.value = []
  selectedDateRange.value = 'all'
  selectedProfileTypes.value = []
  currentSort.value = 'newest'
  handleFilterChange()
}

// Watch for prop changes
watch(() => props.activeTab, (newTab) => {
  selectedTab.value = newTab
})
</script>

<style scoped>
.mobile-filters-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

.mobile-tabs {
  flex: 1;
  overflow-x: auto;
  
  .tab-toggle {
    :deep(.q-btn-toggle) {
      border-radius: 20px;
    }
    
    :deep(.q-btn) {
      font-size: 12px;
      padding: 6px 12px;
      min-width: auto;
    }
  }
}

.filter-actions {
  display: flex;
  gap: 4px;
}

.ai-filter-btn,
.sort-btn,
.filter-btn {
  color: #6b7280;
  
  &:hover {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.1);
  }
}

.ai-filter-card {
  width: 100%;
  max-width: 400px;
  margin: 16px;
}

.ai-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
}

.ai-suggestions {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggestion-chip {
  font-size: 11px;
  height: 24px;
}

.filter-menu-card {
  width: 100%;
  max-height: 70vh;
  margin: 0;
  border-radius: 16px 16px 0 0;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-group-title {
  font-size: 14px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 8px;
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .mobile-filters-container {
    gap: 8px;
  }
  
  .filter-actions {
    gap: 2px;
  }
  
  .ai-filter-btn,
  .sort-btn,
  .filter-btn {
    padding: 6px;
  }
}
</style>
