<template>
  <q-layout view="lHh LpR lFf" class="virtual-community-layout">
    <!-- Fixed Header -->
    <q-header elevated class="glass-header">
      <VirtualCommunityTopBar
        @toggle-left-drawer="toggleLeftDrawer"
        @open-create="openCreateDialog"
        @trigger-ai="handleAITrigger"
      />
    </q-header>

    <!-- Mobile Drawers Only -->
    <q-drawer
      v-if="$q.screen.lt.md"
      v-model="leftDrawerOpen"
      side="left"
      :width="280"
      :breakpoint="768"
      bordered
      class="virtual-community-left-drawer"
    >
      <VirtualCommunityLeftSidebar
        :active-tab="activeTab"
        @navigate="handleNavigation"
      />
    </q-drawer>

    <!-- Page Container -->
    <q-page-container class="virtual-community-page-container">
      <!-- Mobile Filter Bar -->
      <div v-if="$q.screen.lt.md" class="mobile-filter-bar">
        <VirtualCommunityMobileFilters
          :active-tab="activeTab"
          @filter-change="handleFilterChange"
        />
      </div>

      <!-- Main Content Page -->
      <q-page class="virtual-community-page">
        <div class="page-content">
          <!-- Desktop Grid Layout -->
          <div v-if="$q.screen.gt.sm" class="row q-gutter-md">
            <!-- Left Sidebar - col-3 -->
            <div class="col-3">
              <q-card class="left-sidebar-card">
                <VirtualCommunityLeftSidebar
                  :active-tab="activeTab"
                  @navigate="handleNavigation"
                />
              </q-card>
            </div>

            <!-- Main Content - col-6 -->
            <div class="col-6">
              <!-- Featured Section -->
              <div class="featured-section-container scrollable-featured">
                <slot name="featured-section" />
              </div>

              <!-- Main Content -->
              <div class="main-content-container">
                <slot />
              </div>
            </div>

            <!-- Right Sidebar - col-3 -->
            <div class="col-3">
              <q-card class="right-sidebar-card">
                <VirtualCommunityRightSidebar />
              </q-card>
            </div>
          </div>

          <!-- Mobile Layout -->
          <div v-else class="row justify-center">
            <div class="col-12">
              <!-- Featured Section -->
              <div class="featured-section-container scrollable-featured">
                <slot name="featured-section" />
              </div>

              <!-- Main Content -->
              <div class="main-content-container">
                <slot />
              </div>
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>

    <!-- Post Creation Dialog -->
    <PostCreationDialog
      v-model="showCreateDialog"
      :active-tab="activeTab"
      @post-created="handlePostCreated"
    />
  </q-layout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useQuasar } from 'quasar'
import VirtualCommunityTopBar from '../components/layout/VirtualCommunityTopBar.vue'
import VirtualCommunityLeftSidebar from '../components/layout/VirtualCommunityLeftSidebar.vue'
import VirtualCommunityRightSidebar from '../components/layout/VirtualCommunityRightSidebar.vue'
import VirtualCommunityMobileFilters from '../components/layout/VirtualCommunityMobileFilters.vue'
import PostCreationDialog from '../components/feed/PostCreationDialog.vue'

// Props
interface Props {
  activeTab?: string
}

const props = withDefaults(defineProps<Props>(), {
  activeTab: 'feed'
})

// Emits
const emit = defineEmits<{
  'tab-change': [tab: string]
  'ai-trigger': [query: string]
  'post-created': [post: any]
  'filter-change': [filters: any]
}>()

// Composables
const route = useRoute()
const $q = useQuasar()

// State
const leftDrawerOpen = ref(false) // Only for mobile
const showCreateDialog = ref(false)

// Computed
const activeTab = computed(() => {
  return props.activeTab || route.query.tab as string || 'feed'
})

// Methods
function toggleLeftDrawer() {
  leftDrawerOpen.value = !leftDrawerOpen.value
}

function openCreateDialog() {
  showCreateDialog.value = true
}

function handleAITrigger(query: string) {
  emit('ai-trigger', query)
}

function handleNavigation(tab: string) {
  emit('tab-change', tab)
  // Close mobile drawer after navigation
  if ($q.screen.lt.md) {
    leftDrawerOpen.value = false
  }
}

function handlePostCreated(post: any) {
  emit('post-created', post)
  showCreateDialog.value = false
}

function handleFilterChange(filters: any) {
  emit('filter-change', filters)
}

// No lifecycle hooks needed for grid layout
</script>

<style scoped>
.virtual-community-layout {
  background: #f8fafc;
}

.glass-header {
  background: rgba(223, 239, 230, 0.8) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(13, 138, 62, 0.1);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.virtual-community-left-drawer {
  background: white;
  border-right: 1px solid #e5e7eb;
}

.virtual-community-right-drawer {
  background: white;
  border-left: 1px solid #e5e7eb;
}

.virtual-community-page-container {
  background: #f8fafc;
}

.mobile-filter-bar {
  position: fixed;
  top: 64px; /* Fixed position below the header (header height is 64px) */
  left: 0;
  right: 0;
  z-index: 999; /* Slightly lower than header but above content */
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 8px 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 0; /* Remove any margin */
}

/* Grid-based sidebar cards */
.left-sidebar-card {
  position: sticky;
  top: 80px; /* Below header with some margin */
  height: calc(100vh - 96px); /* Full height minus header and margin */
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.right-sidebar-card {
  position: sticky;
  top: 80px; /* Below header with some margin */
  height: calc(100vh - 96px); /* Full height minus header and margin */
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Page content styling */
.virtual-community-page {
  min-height: 100vh;
}

/* Remove padding to eliminate unwanted space on both mobile and desktop */
.virtual-community-page {
  padding-top: 0; /* Remove padding to eliminate space between header and content */
}

.page-content {
  padding: 16px;
}

.featured-section-container {
  margin-bottom: 24px;
}

.scrollable-featured {
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 8px;
}

.main-content-container {
  padding: 0;
}

/* Custom scrollbar styling for horizontal featured section */
.scrollable-featured {
  /* Firefox scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: #0D8A3E #f1f1f1;
}

.scrollable-featured::-webkit-scrollbar {
  height: 8px;
}

.scrollable-featured::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
  margin: 0 16px; /* Add margin to match content padding */
}

.scrollable-featured::-webkit-scrollbar-thumb {
  background: #0D8A3E;
  border-radius: 4px;
}

.scrollable-featured::-webkit-scrollbar-thumb:hover {
  background: #0a6b31;
}

/* Responsive adjustments */
@media (max-width: 1023px) {
  .page-content {
    padding: 8px;
  }

  .featured-section-container {
    margin-bottom: 16px;
  }

  .scrollable-feed {
    height: calc(100vh - 64px - 16px - 100px);
  }
}

@media (max-width: 767px) {
  .page-content {
    padding: 4px;
  }

  .scrollable-feed {
    height: calc(100vh - 64px - 8px - 80px);
    padding-right: 4px;
  }

  /* On mobile, use full width instead of col-7 */
  .col-md-7 {
    max-width: 100% !important;
    flex: 0 0 100% !important;
  }
}
</style>
