<template>
  <q-toolbar class="virtual-community-toolbar">
    <!-- Left Section: Hamburger Menu -->
    <q-btn
      flat
      dense
      round
      icon="menu"
      aria-label="Menu"
      class="hamburger-btn"
      @click="$emit('toggle-left-drawer')"
    />

    <!-- Center Section: Logo and AI Search -->
    <div class="toolbar-center">
      <!-- Logo -->
      <router-link to="/" class="logo-link">
        <img
          src="/smile-factory-logo.svg"
          alt="Smile Factory Logo"
          class="toolbar-logo"
        />
      </router-link>

      <!-- AI Search Input -->
      <div class="ai-search-container">
        <!-- Desktop: Full search input -->
        <q-input
          v-model="searchQuery"
          outlined
          dense
          placeholder="Ask AI anything about the community..."
          class="ai-search-input desktop-only"
          @keyup.enter="handleAISearch"
        >
          <template v-slot:prepend>
            <q-icon name="smart_toy" class="ai-icon" />
          </template>
          <template v-slot:append>
            <q-btn
              flat
              dense
              round
              icon="send"
              class="search-btn"
              @click="handleAISearch"
              :disable="!searchQuery.trim()"
            />
          </template>
        </q-input>

        <!-- Mobile: Search icon only -->
        <q-btn
          flat
          dense
          round
          icon="search"
          class="mobile-search-btn mobile-only"
          @click="handleMobileSearch"
        />
      </div>
    </div>

    <!-- Right Section: Actions -->
    <div class="toolbar-actions">
      <!-- Create Post Button -->
      <!-- Desktop: Button with text -->
      <q-btn
        unelevated
        rounded
        color="primary"
        icon="add"
        label="Create"
        class="create-btn desktop-only"
        @click="$emit('open-create')"
      />

      <!-- Mobile: Icon only -->
      <q-btn
        round
        color="primary"
        icon="add"
        class="create-btn mobile-only"
        @click="$emit('open-create')"
      />

      <!-- Notifications -->
      <q-btn
        flat
        dense
        round
        class="notification-btn"
        @click="toggleNotifications"
      >
        <q-icon name="notifications" />
        <q-badge
          v-if="unreadCount > 0"
          color="red"
          floating
          rounded
          :label="unreadCount > 99 ? '99+' : unreadCount"
        />
      </q-btn>

      <!-- User Menu -->
      <q-btn
        flat
        dense
        round
        class="user-menu-btn"
      >
        <q-icon name="account_circle" size="32px" />
        <q-menu>
          <q-list style="min-width: 200px">
            <q-item clickable v-close-popup @click="goToDashboard">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>Dashboard</q-item-section>
            </q-item>
            <q-item clickable v-close-popup @click="goToProfile">
              <q-item-section avatar>
                <q-icon name="person" />
              </q-item-section>
              <q-item-section>Profile</q-item-section>
            </q-item>
            <q-separator />
            <q-item clickable v-close-popup @click="logout">
              <q-item-section avatar>
                <q-icon name="logout" />
              </q-item-section>
              <q-item-section>Logout</q-item-section>
            </q-item>
          </q-list>
        </q-menu>
      </q-btn>
    </div>

    <!-- Notifications Panel -->
    <q-menu
      v-model="showNotifications"
      anchor="bottom right"
      self="top right"
      class="notifications-menu"
    >
      <div class="notifications-panel">
        <div class="notifications-header">
          <h6>Notifications</h6>
          <q-btn
            flat
            dense
            round
            icon="close"
            @click="showNotifications = false"
          />
        </div>
        <NotificationList />
      </div>
    </q-menu>
  </q-toolbar>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useNotificationStore } from '../../stores/notifications'
import NotificationList from '../common/NotificationList.vue'

// Emits
const emit = defineEmits<{
  'toggle-left-drawer': []
  'open-create': []
  'trigger-ai': [query: string]
  'mobile-search': []
}>()

// Composables
const router = useRouter()
const authStore = useAuthStore()
const notificationStore = useNotificationStore()

// State
const searchQuery = ref('')
const showNotifications = ref(false)

// Computed
const unreadCount = computed(() => notificationStore.unreadCount)

// Methods
function handleAISearch() {
  if (searchQuery.value.trim()) {
    emit('trigger-ai', searchQuery.value.trim())
    searchQuery.value = ''
  }
}

function handleMobileSearch() {
  // For mobile, we could open a search dialog or navigate to a search page
  emit('mobile-search')
}

function toggleNotifications() {
  showNotifications.value = !showNotifications.value
}

function goToDashboard() {
  router.push('/dashboard')
}

function goToProfile() {
  router.push('/dashboard/profile')
}

function logout() {
  authStore.logout()
  router.push('/')
}
</script>

<style scoped>
.virtual-community-toolbar {
  height: 64px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.hamburger-btn {
  color: #0D8A3E;

  &:hover {
    background: rgba(13, 138, 62, 0.1);
  }
}

.toolbar-center {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.toolbar-logo {
  height: 40px;
  width: auto;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.ai-search-container {
  flex: 1;
  max-width: 500px;
}

.ai-search-input {
  :deep(.q-field__control) {
    border-radius: 24px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
  }
  
  :deep(.q-field__native) {
    padding-left: 8px;
  }
}

.ai-icon {
  color: #0D8A3E;
}

.search-btn {
  color: #0D8A3E;
}

.toolbar-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.create-btn {
  font-weight: 600;
  padding: 8px 16px;
  
  @media (max-width: 768px) {
    .q-btn__content {
      .q-icon {
        margin-right: 0;
      }
      
      span:not(.q-icon) {
        display: none;
      }
    }
  }
}

.notification-btn,
.user-menu-btn {
  color: #0D8A3E;

  &:hover {
    background: rgba(13, 138, 62, 0.1);
  }
}



.notifications-menu {
  margin-top: 8px;
}

.notifications-panel {
  width: 350px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  
  h6 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .toolbar-center {
    gap: 16px;
  }
  
  .ai-search-container {
    max-width: 300px;
  }
}

@media (max-width: 768px) {
  .virtual-community-toolbar {
    padding: 0 12px;
    gap: 12px;
  }
  
  .toolbar-center {
    gap: 12px;
  }
  
  .ai-search-container {
    max-width: 200px;
  }
  
  .ai-search-input {
    :deep(.q-field__control) {
      font-size: 14px;
    }
  }
}

/* Mobile search button styles */
.mobile-search-btn {
  color: rgba(255, 255, 255, 0.8);
}

.mobile-search-btn:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
}

/* Responsive classes */
.desktop-only {
  display: flex;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .desktop-only {
    display: none !important;
  }

  .mobile-only {
    display: flex !important;
  }

  .ai-search-container {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .toolbar-logo {
    height: 32px;
  }
  
  .ai-search-container {
    max-width: 150px;
  }
  
  .toolbar-actions {
    gap: 4px;
  }
}
</style>
