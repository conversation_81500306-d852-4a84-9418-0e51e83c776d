<template>
  <q-card 
    class="featured-card cursor-pointer"
    :class="[`featured-card--${size}`, { 'featured-card--hover': !loading }]"
    @click="handleReadMore"
  >
    <!-- Featured Image -->
    <div class="featured-image-container">
      <q-img
        :src="articleImage"
        :ratio="imageRatio"
        class="featured-image"
        loading="lazy"
        @error="handleImageError"
      >
        <template v-slot:error>
          <div class="absolute-full flex flex-center bg-grey-3">
            <q-icon name="image" size="3em" color="grey-5" />
          </div>
        </template>
        


        <!-- Overlay Content -->
        <div class="absolute-bottom featured-overlay">
          <div class="featured-content q-pa-md">
            <!-- Category -->
            <q-chip
              v-if="article.category"
              :color="getCategoryColor(article.category)"
              text-color="white"
              size="sm"
              class="q-mb-sm"
            >
              {{ article.category }}
            </q-chip>

            <!-- Title -->
            <h3 class="featured-title text-white q-mb-sm" :class="titleClass">
              {{ articleTitle }}
            </h3>

            <!-- Excerpt (only for large cards) -->
            <p v-if="size === 'large' && article.excerpt" class="featured-excerpt text-white q-mb-md">
              {{ truncatedExcerpt }}
            </p>

            <!-- Meta Information -->
            <div class="featured-meta row items-center text-white q-mb-sm">
              <div class="meta-item row items-center q-mr-md">
                <q-icon name="schedule" size="sm" class="q-mr-xs" />
                <span class="text-caption">{{ formatDate(article.createdAt) }}</span>
              </div>
              <div v-if="readTime" class="meta-item row items-center">
                <q-icon name="visibility" size="sm" class="q-mr-xs" />
                <span class="text-caption">{{ readTime }}</span>
              </div>
            </div>

            <!-- Tags (for large cards - main featured blog) -->
            <div v-if="size === 'large' && articleTags.length > 0" class="featured-tags-large">
              <q-chip
                v-for="tag in articleTags.slice(0, 3)"
                :key="tag"
                size="sm"
                outline
                color="white"
                text-color="white"
                class="q-mr-xs q-mb-xs tag-chip"
              >
                #{{ tag }}
              </q-chip>
            </div>
          </div>
        </div>
      </q-img>
    </div>



    <!-- Tags (for medium/small cards) -->
    <div v-if="size !== 'large' && articleTags.length > 0" class="featured-tags q-pa-sm">
      <q-chip
        v-for="tag in articleTags.slice(0, 2)"
        :key="tag"
        size="sm"
        outline
        color="primary"
        class="q-mr-xs"
      >
        #{{ tag }}
      </q-chip>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { Post } from '@/types/post';

// Props
interface Props {
  article: Post;
  size?: 'large' | 'medium' | 'small';
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium'
});

// Emits
const emit = defineEmits<{
  read: [id: number];
}>();

// State
const loading = ref(false);
const fixedImageUrl = ref<string | null>(null);

// Computed
const articleTitle = computed(() => {
  return props.article.blogTitle || props.article.title || 'Untitled Article';
});

const articleImage = computed(() => {
  if (fixedImageUrl.value) {
    return fixedImageUrl.value;
  }
  return props.article.featuredImage || 
         props.article.image || 
         'https://picsum.photos/800/600?random=' + props.article.id;
});

const articleTags = computed(() => {
  if (!props.article.tags) return [];
  return props.article.tags.filter(tag => 
    typeof tag === 'string' && 
    tag.toLowerCase() !== 'featured' && 
    tag.toLowerCase() !== 'blog'
  );
});

const imageRatio = computed(() => {
  switch (props.size) {
    case 'large': return 16/9;
    case 'medium': return 4/3;
    case 'small': return 3/2;
    default: return 4/3;
  }
});

const titleClass = computed(() => {
  switch (props.size) {
    case 'large': return 'text-h4 text-weight-bold';
    case 'medium': return 'text-h6 text-weight-medium';
    case 'small': return 'text-subtitle1 text-weight-medium';
    default: return 'text-h6 text-weight-medium';
  }
});

const truncatedExcerpt = computed(() => {
  if (!props.article.excerpt) return '';
  const maxLength = props.size === 'large' ? 150 : 80;
  return props.article.excerpt.length > maxLength 
    ? props.article.excerpt.substring(0, maxLength) + '...'
    : props.article.excerpt;
});

const readTime = computed(() => {
  // Calculate read time based on content length
  const content = props.article.blogFullContent || props.article.content || '';
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  const minutes = Math.ceil(wordCount / wordsPerMinute);
  return `${minutes} min read`;
});

// Methods
function getCategoryColor(category: string): string {
  const colors: Record<string, string> = {
    technology: 'blue',
    innovation: 'purple',
    entrepreneurship: 'green',
    'success-stories': 'orange',
    events: 'red',
    news: 'teal',
    default: 'primary'
  };
  return colors[category.toLowerCase()] || colors.default;
}

function formatDate(dateString?: string): string {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
}

function handleImageError() {
  // Set a fallback image
  fixedImageUrl.value = `https://picsum.photos/800/600?random=${props.article.id}`;
}

function handleReadMore() {
  emit('read', props.article.id);
}
</script>

<style scoped>
.featured-card {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.featured-card--hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.featured-card--large {
  min-height: 350px; /* Reduced from 400px for better proportions */
}

.featured-card--medium {
  min-height: 240px; /* Reduced from 280px */
}

.featured-card--small {
  min-height: 160px; /* Reduced from 200px for more compact secondary cards */
}

.featured-image-container {
  position: relative;
  height: 100%;
}

.featured-image {
  height: 100%;
}

.featured-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.85) 0%,
    rgba(0, 0, 0, 0.6) 40%,
    rgba(0, 0, 0, 0.3) 70%,
    transparent 100%
  );
}

.featured-content {
  position: relative;
  z-index: 2;
}

.featured-title {
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.featured-excerpt {
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.featured-meta {
  opacity: 0.9;
}

.meta-item {
  font-size: 0.875rem;
}

/* Tags styling for large featured cards */
.featured-tags-large {
  margin-top: 8px;
}

.featured-tags-large .tag-chip {
  background: rgba(255, 255, 255, 0.15) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(4px);
  font-size: 0.75rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.featured-tags {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
}

@media (max-width: 768px) {
  .featured-card--large {
    min-height: 300px;
  }
  
  .featured-card--medium {
    min-height: 240px;
  }
  
  .featured-title.text-h4 {
    font-size: 1.5rem;
  }
  
  .featured-actions {
    opacity: 1; /* Always visible on mobile */
  }
}
</style>
