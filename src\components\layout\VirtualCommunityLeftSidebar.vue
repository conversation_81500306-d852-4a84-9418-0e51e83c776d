<template>
  <div class="virtual-community-left-sidebar">
    <!-- Community Header -->
    <div class="sidebar-header">
      <div class="community-title">
        <q-icon name="groups" class="community-icon" />
        <span>Virtual Community</span>
      </div>
      <q-btn
        flat
        dense
        round
        icon="refresh"
        class="refresh-btn"
        @click="refreshContent"
      />
    </div>

    <!-- Navigation Menu -->
    <div class="navigation-menu">
      <q-list>
        <!-- Feed Tab -->
        <q-item
          clickable
          :active="activeTab === 'feed'"
          @click="navigateToTab('feed')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="dynamic_feed" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Feed</q-item-label>
            <q-item-label caption>Latest updates</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Profiles Tab -->
        <q-item
          clickable
          :active="activeTab === 'profiles'"
          @click="navigateToTab('profiles')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="people" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Profiles</q-item-label>
            <q-item-label caption>Community members</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Events Tab -->
        <q-item
          clickable
          :active="activeTab === 'events'"
          @click="navigateToTab('events')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="event" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Events</q-item-label>
            <q-item-label caption>Upcoming activities</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Blog Tab -->
        <q-item
          clickable
          :active="activeTab === 'blog'"
          @click="navigateToTab('blog')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="article" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Blog</q-item-label>
            <q-item-label caption>Stories & insights</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Marketplace Tab -->
        <q-item
          clickable
          :active="activeTab === 'marketplace'"
          @click="navigateToTab('marketplace')"
          class="nav-item"
        >
          <q-item-section avatar>
            <q-icon name="store" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Marketplace</q-item-label>
            <q-item-label caption>Products & services</q-item-label>
          </q-item-section>
        </q-item>

        <!-- Groups Tab (Coming Soon) -->
        <q-item
          clickable
          :active="activeTab === 'groups'"
          @click="navigateToTab('groups')"
          class="nav-item coming-soon"
          disable
        >
          <q-item-section avatar>
            <q-icon name="group_work" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Groups</q-item-label>
            <q-item-label caption>
              <span class="coming-soon-text">Coming Soon</span>
            </q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <q-separator class="q-my-md" />
      
      <div class="section-title">Quick Actions</div>
      
      <q-list>
        <!-- AI Triggers -->
        <q-item
          clickable
          @click="triggerAI('Show me trending posts')"
          class="action-item"
        >
          <q-item-section avatar>
            <q-icon name="trending_up" color="orange" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Trending Posts</q-item-label>
          </q-item-section>
        </q-item>

        <q-item
          clickable
          @click="triggerAI('Find mentors in my field')"
          class="action-item"
        >
          <q-item-section avatar>
            <q-icon name="school" color="blue" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Find Mentors</q-item-label>
          </q-item-section>
        </q-item>

        <q-item
          clickable
          @click="triggerAI('Show upcoming events')"
          class="action-item"
        >
          <q-item-section avatar>
            <q-icon name="schedule" color="green" />
          </q-item-section>
          <q-item-section>
            <q-item-label>Upcoming Events</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </div>

    <!-- Community Stats -->
    <div class="community-stats">
      <q-separator class="q-my-md" />
      
      <div class="section-title">Community Stats</div>
      
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-number">{{ memberCount }}</div>
          <div class="stat-label">Members</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ postCount }}</div>
          <div class="stat-label">Posts</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ eventCount }}</div>
          <div class="stat-label">Events</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { feedDataService } from '../../services/feedDataService'

// Props
interface Props {
  activeTab: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'navigate': [tab: string]
  'ai-trigger': [query: string]
  'refresh': []
}>()

// Composables
const router = useRouter()
// feedDataService is imported as singleton

// State
const memberCount = ref(0)
const postCount = ref(0)
const eventCount = ref(0)

// Methods
function navigateToTab(tab: string) {
  emit('navigate', tab)
  router.push({ 
    path: '/newvirtual-community', 
    query: { tab } 
  })
}

function triggerAI(query: string) {
  emit('ai-trigger', query)
}

function refreshContent() {
  emit('refresh')
  loadStats()
}

async function loadStats() {
  try {
    // Load community statistics
    const stats = await feedDataService.getCommunityStats()
    memberCount.value = stats.members || 0
    postCount.value = stats.posts || 0
    eventCount.value = stats.events || 0
  } catch (error) {
    console.error('Failed to load community stats:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.virtual-community-left-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.community-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #111827;
  font-size: 16px;
}

.community-icon {
  color: #6366f1;
  font-size: 20px;
}

.refresh-btn {
  color: #6b7280;
  
  &:hover {
    color: #6366f1;
    background: rgba(99, 102, 241, 0.1);
  }
}

.navigation-menu {
  flex: 1;
  padding: 8px 0;
}

.nav-item {
  margin: 2px 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(99, 102, 241, 0.05);
  }
  
  &.q-item--active {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
    
    .q-icon {
      color: #6366f1;
    }
  }
  
  &.coming-soon {
    opacity: 0.6;
  }
}

.coming-soon-text {
  color: #f59e0b;
  font-size: 11px;
  font-weight: 500;
}

.quick-actions,
.community-stats {
  padding: 0 16px 16px;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.action-item {
  margin: 2px 0;
  border-radius: 6px;
  
  &:hover {
    background: rgba(0, 0, 0, 0.03);
  }
  
  .q-item-label {
    font-size: 14px;
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-top: 8px;
}

.stat-item {
  text-align: center;
  padding: 8px 4px;
  border-radius: 6px;
  background: #f9fafb;
}

.stat-number {
  font-size: 16px;
  font-weight: 700;
  color: #111827;
  line-height: 1;
}

.stat-label {
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 4px;
  }
  
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    padding: 6px 8px;
  }
  
  .stat-number {
    font-size: 14px;
  }
  
  .stat-label {
    font-size: 12px;
    margin-top: 0;
  }
}
</style>
