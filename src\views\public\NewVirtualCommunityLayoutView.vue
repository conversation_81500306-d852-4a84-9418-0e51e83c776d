<template>
  <VirtualCommunityLayout
    :active-tab="activeTab"
    @tab-change="handleTabChange"
    @ai-trigger="handleAITrigger"
    @post-created="handlePostCreated"
    @filter-change="handleFilterChange"
  >
    <!-- Featured Section Slot -->
    <template #featured-section>
      <FeaturedSection
        :tab="activeTab"
        :loading="featuredLoading"
        @refresh="refreshFeaturedContent"
      />
    </template>

    <!-- Main Content -->
    <div class="virtual-community-content">
      <!-- Feed Tab -->
      <div v-if="activeTab === 'feed'" class="tab-content">
        <div class="posts-grid">
          <PostCard
            v-for="post in posts"
            :key="post.id"
            :post="post"
            @like="handleLike"
            @comment="handleComment"
            @share="handleShare"
          />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMore && posts.length > 0" class="load-more-container">
          <q-btn
            unelevated
            color="primary"
            label="Load More Posts"
            @click="loadMorePosts"
            :loading="loading"
          />
        </div>

        <!-- Empty State -->
        <div v-if="!loading && posts.length === 0" class="empty-state">
          <q-icon name="dynamic_feed" size="4rem" color="grey-5" />
          <h4>No Posts Yet</h4>
          <p>Be the first to share something with the community!</p>
        </div>
      </div>

      <!-- Profiles Tab -->
      <div v-else-if="activeTab === 'profiles'" class="tab-content">
        <div class="profiles-grid">
          <ProfileCard
            v-for="profile in profiles"
            :key="profile.id"
            :profile="profile"
            @connect="handleConnect"
            @message="handleMessage"
          />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMoreProfiles && profiles.length > 0" class="load-more-container">
          <q-btn
            unelevated
            color="primary"
            label="Load More Profiles"
            @click="loadMoreProfiles"
            :loading="loadingProfiles"
          />
        </div>

        <!-- Empty State -->
        <div v-if="!loadingProfiles && profiles.length === 0" class="empty-state">
          <q-icon name="people" size="4rem" color="grey-5" />
          <h4>No Profiles Found</h4>
          <p>No community members match your current filters.</p>
        </div>
      </div>

      <!-- Events Tab -->
      <div v-else-if="activeTab === 'events'" class="tab-content">
        <div class="events-grid">
          <EventCard
            v-for="event in events"
            :key="event.id"
            :event="event"
            @register="handleEventRegister"
            @share="handleShare"
          />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMoreEvents && events.length > 0" class="load-more-container">
          <q-btn
            unelevated
            color="primary"
            label="Load More Events"
            @click="loadMoreEvents"
            :loading="loadingEvents"
          />
        </div>

        <!-- Empty State -->
        <div v-if="!loadingEvents && events.length === 0" class="empty-state">
          <q-icon name="event" size="4rem" color="grey-5" />
          <h4>No Events Found</h4>
          <p>No upcoming events match your current filters.</p>
        </div>
      </div>

      <!-- Blog Tab -->
      <div v-else-if="activeTab === 'blog'" class="tab-content">
        <BlogLayout
          :articles="articles"
          :loading="loadingArticles"
          :has-more="hasMoreArticles"
          @load-more="loadMoreArticles"
          @refresh="refreshArticles"
        />
      </div>

      <!-- Marketplace Tab -->
      <div v-else-if="activeTab === 'marketplace'" class="tab-content">
        <div class="marketplace-grid">
          <MarketplaceCard
            v-for="item in marketplace"
            :key="item.id"
            :listing="item"
            @contact="handleContact"
            @share="handleShare"
          />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMoreMarketplace && marketplace.length > 0" class="load-more-container">
          <q-btn
            unelevated
            color="primary"
            label="Load More Items"
            @click="loadMoreMarketplace"
            :loading="loadingMarketplace"
          />
        </div>

        <!-- Empty State -->
        <div v-if="!loadingMarketplace && marketplace.length === 0" class="empty-state">
          <q-icon name="store" size="4rem" color="grey-5" />
          <h4>No Items Found</h4>
          <p>No marketplace items match your current filters.</p>
        </div>
      </div>

      <!-- Groups Tab (Coming Soon) -->
      <div v-else-if="activeTab === 'groups'" class="tab-content">
        <div class="coming-soon-content">
          <q-icon name="group_work" size="4rem" color="grey-5" />
          <h4>Groups Coming Soon</h4>
          <p>We're working on bringing you community groups. Stay tuned!</p>
        </div>
      </div>


    </div>

    <!-- AI Chat Placeholder -->
    <div v-if="showAIChat" class="ai-chat-placeholder">
      <div class="ai-chat-content">
        <h3>AI Chat Assistant</h3>
        <p>Query: {{ aiQuery }}</p>
        <button @click="closeAIChat">Close</button>
      </div>
    </div>
  </VirtualCommunityLayout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useFilterStore } from '../../stores/filterStore'
import { useNotificationStore } from '../../stores/notifications'
import { useContentInteractions } from '../../composables/useContentInteractions'
import { useGlobalServicesStore } from '../../stores/globalServices'
import { useProfileStore } from '../../stores/profile'
import { useAuthStore } from '../../stores/auth'
import { feedDataService } from '../../services/feedDataService'
import VirtualCommunityLayout from '../../layouts/VirtualCommunityLayout.vue'
import FeaturedSection from '../../components/feed/FeaturedSection.vue'
import PostCard from '../../components/feed/cards/PostCard.vue'
import ProfileCard from '../../components/feed/cards/ProfileCard.vue'
import EventCard from '../../components/feed/cards/EventCard.vue'
import MarketplaceCard from '../../components/feed/cards/MarketplaceCard.vue'
import BlogLayout from '../../components/blog/BlogLayout.vue'
import PostCreationDialog from '../../components/feed/PostCreationDialog.vue'

// Composables
const route = useRoute()
const router = useRouter()
const $q = useQuasar()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const filterStore = useFilterStore()
const notificationStore = useNotificationStore()
const globalServicesStore = useGlobalServicesStore()
const { likePost, sharePost, commentOnPost } = useContentInteractions()

// State
const posts = ref([])
const profiles = ref([])
const events = ref([])
const articles = ref([])
const marketplace = ref([])

const loading = ref(false)
const loadingProfiles = ref(false)
const loadingEvents = ref(false)
const loadingArticles = ref(false)
const loadingMarketplace = ref(false)
const featuredLoading = ref(false)

const hasMore = ref(true)
const hasMoreProfiles = ref(true)
const hasMoreEvents = ref(true)
const hasMoreArticles = ref(true)
const hasMoreMarketplace = ref(true)

const showAIChat = ref(false)
const aiQuery = ref('')
const currentPage = ref(1)
const currentFilters = ref({})

// Computed
const activeTab = computed(() => {
  return route.query.tab as string || 'feed'
})

const aiContext = computed(() => ({
  tab: activeTab.value,
  filters: currentFilters.value,
  hasContent: {
    posts: posts.value.length > 0,
    profiles: profiles.value.length > 0,
    events: events.value.length > 0,
    articles: articles.value.length > 0,
    marketplace: marketplace.value.length > 0
  }
}))

// Methods
function handleTabChange(tab: string) {
  router.push({ 
    path: '/newvirtual-community', 
    query: { ...route.query, tab } 
  })
}

function handleAITrigger(query: string) {
  aiQuery.value = query
  showAIChat.value = true
}

function closeAIChat() {
  showAIChat.value = false
  aiQuery.value = ''
}

function handlePostCreated(post: any) {
  // Add new post to the beginning of the posts array
  posts.value.unshift(post)

  $q.notify({
    type: 'positive',
    message: 'Post created successfully!',
    position: 'top'
  })
}

function handleFilterChange(filters: any) {
  currentFilters.value = filters
  currentPage.value = 1

  // Reload content based on active tab
  switch (activeTab.value) {
    case 'feed':
      loadPosts()
      break
    case 'profiles':
      loadProfiles()
      break
    case 'events':
      loadEvents()
      break
    case 'blog':
      loadArticles()
      break
    case 'marketplace':
      loadMarketplace()
      break
  }
}

// Content interaction methods
async function handleLike(postId: string) {
  try {
    await likePost(postId)
    // Update the post in the local array
    const post = posts.value.find(p => p.id === postId)
    if (post) {
      post.likes_count = (post.likes_count || 0) + 1
      post.user_has_liked = true
    }
  } catch (error) {
    console.error('Failed to like post:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to like post'
    })
  }
}

async function handleComment(postId: string) {
  // This would typically open a comment dialog
  console.log('Comment on post:', postId)
}

async function handleShare(item: any) {
  try {
    await sharePost(item.id)
    $q.notify({
      type: 'positive',
      message: 'Shared successfully!'
    })
  } catch (error) {
    console.error('Failed to share:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to share'
    })
  }
}

async function handleConnect(profileId: string) {
  // This would typically send a connection request
  console.log('Connect with profile:', profileId)
  $q.notify({
    type: 'positive',
    message: 'Connection request sent!'
  })
}

async function handleMessage(profileId: string) {
  // This would typically open a message dialog
  console.log('Message profile:', profileId)
}

async function handleEventRegister(eventId: string) {
  // This would typically register for an event
  console.log('Register for event:', eventId)
  $q.notify({
    type: 'positive',
    message: 'Registered for event!'
  })
}

async function handleContact(itemId: string) {
  // This would typically contact the seller
  console.log('Contact seller for item:', itemId)
}

// Content loading methods
async function loadPosts() {
  if (loading.value) return

  try {
    loading.value = true
    const result = await feedDataService.fetchFeedPosts(
      { page: currentPage.value, limit: 10 },
      currentFilters.value
    )

    if (currentPage.value === 1) {
      posts.value = result.posts || []
    } else {
      posts.value.push(...(result.posts || []))
    }

    hasMore.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load posts:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load posts'
    })
  } finally {
    loading.value = false
  }
}

async function loadProfiles() {
  if (loadingProfiles.value) return

  try {
    loadingProfiles.value = true
    const result = await feedDataService.fetchProfiles(
      { page: currentPage.value, limit: 10 },
      currentFilters.value
    )

    if (currentPage.value === 1) {
      profiles.value = result.profiles || []
    } else {
      profiles.value.push(...(result.profiles || []))
    }

    hasMoreProfiles.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load profiles:', error)
  } finally {
    loadingProfiles.value = false
  }
}

async function loadEvents() {
  if (loadingEvents.value) return

  try {
    loadingEvents.value = true
    const result = await feedDataService.fetchEvents(
      { page: currentPage.value, limit: 10 },
      currentFilters.value
    )

    if (currentPage.value === 1) {
      events.value = result.events || []
    } else {
      events.value.push(...(result.events || []))
    }

    hasMoreEvents.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load events:', error)
  } finally {
    loadingEvents.value = false
  }
}

async function loadArticles() {
  if (loadingArticles.value) return

  try {
    loadingArticles.value = true
    const result = await feedDataService.fetchArticles(
      { page: currentPage.value, limit: 10 },
      currentFilters.value
    )

    if (currentPage.value === 1) {
      articles.value = result.articles || []
    } else {
      articles.value.push(...(result.articles || []))
    }

    hasMoreArticles.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load articles:', error)
  } finally {
    loadingArticles.value = false
  }
}

async function loadMarketplace() {
  if (loadingMarketplace.value) return

  try {
    loadingMarketplace.value = true
    const result = await feedDataService.fetchMarketplace(
      { page: currentPage.value, limit: 10 },
      currentFilters.value
    )

    if (currentPage.value === 1) {
      marketplace.value = result.marketplace || []
    } else {
      marketplace.value.push(...(result.marketplace || []))
    }

    hasMoreMarketplace.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load marketplace:', error)
  } finally {
    loadingMarketplace.value = false
  }
}

// Load more methods
function loadMorePosts() {
  if (hasMore.value && !loading.value) {
    currentPage.value++
    loadPosts()
  }
}

function loadMoreProfiles() {
  if (hasMoreProfiles.value && !loadingProfiles.value) {
    currentPage.value++
    loadProfiles()
  }
}

function loadMoreEvents() {
  if (hasMoreEvents.value && !loadingEvents.value) {
    currentPage.value++
    loadEvents()
  }
}

function loadMoreArticles() {
  if (hasMoreArticles.value && !loadingArticles.value) {
    currentPage.value++
    loadArticles()
  }
}

function loadMoreMarketplace() {
  if (hasMoreMarketplace.value && !loadingMarketplace.value) {
    currentPage.value++
    loadMarketplace()
  }
}

// Refresh methods
function refreshFeaturedContent() {
  featuredLoading.value = true
  setTimeout(() => {
    featuredLoading.value = false
  }, 1000)
}

function refreshArticles() {
  currentPage.value = 1
  loadArticles()
}

// Content loading based on tab
async function loadContent(tab: string) {
  currentPage.value = 1

  switch (tab) {
    case 'feed':
      await loadPosts()
      break
    case 'profiles':
      await loadProfiles()
      break
    case 'events':
      await loadEvents()
      break
    case 'blog':
      await loadArticles()
      break
    case 'marketplace':
      await loadMarketplace()
      break
  }
}

// Watch for tab changes
watch(activeTab, (newTab) => {
  loadContent(newTab)
})

// Lifecycle
onMounted(() => {
  loadContent(activeTab.value)
})
</script>

<style scoped>
.virtual-community-content {
  min-height: 400px;
}

.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

/* Content grids */
.posts-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.profiles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
  max-width: 1000px;
  margin: 0 auto;
}

.marketplace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Load more and empty states */
.load-more-container {
  text-align: center;
  padding: 24px;
  margin-top: 16px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-state h4 {
  margin: 16px 0 8px 0;
  color: #374151;
}

.empty-state p {
  margin: 0 0 24px 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.coming-soon-content {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.coming-soon-content h4 {
  margin: 16px 0 8px 0;
  color: #374151;
}

.coming-soon-content p {
  margin: 0 0 24px 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.ai-chat-placeholder {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.ai-chat-content {
  background: white;
  padding: 24px;
  border-radius: 8px;
  max-width: 400px;
  text-align: center;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
