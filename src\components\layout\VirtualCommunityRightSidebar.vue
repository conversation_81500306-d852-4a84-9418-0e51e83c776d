<template>
  <div class="virtual-community-right-sidebar">
    <!-- Community Announcements -->
    <div class="announcements-section">
      <div class="section-header">
        <h6 class="section-title">
          <q-icon name="campaign" class="section-icon" />
          Community Announcements
        </h6>
        <q-btn
          flat
          dense
          round
          icon="refresh"
          size="sm"
          @click="refreshAnnouncements"
          class="refresh-btn"
        />
      </div>

      <div class="announcements-list">
        <div
          v-for="announcement in announcements"
          :key="announcement.id"
          class="announcement-card"
        >
          <div class="announcement-header">
            <q-icon 
              :name="announcement.icon" 
              :color="announcement.color"
              class="announcement-icon"
            />
            <span class="announcement-date">
              {{ formatDate(announcement.created_at) }}
            </span>
          </div>
          <h6 class="announcement-title">{{ announcement.title }}</h6>
          <p class="announcement-content">{{ announcement.content }}</p>
          <q-btn
            v-if="announcement.link"
            flat
            dense
            color="primary"
            :label="announcement.link_text || 'Learn More'"
            @click="openLink(announcement.link)"
            class="announcement-link"
          />
        </div>

        <!-- Empty State -->
        <div v-if="announcements.length === 0" class="empty-state">
          <q-icon name="info" size="md" color="grey-5" />
          <p>No announcements at this time</p>
        </div>
      </div>
    </div>

    <!-- Trending Topics -->
    <div class="trending-section">
      <div class="section-header">
        <h6 class="section-title">
          <q-icon name="trending_up" class="section-icon" />
          Trending Topics
        </h6>
      </div>

      <div class="trending-list">
        <q-chip
          v-for="topic in trendingTopics"
          :key="topic.id"
          clickable
          color="primary"
          text-color="white"
          :label="`#${topic.name}`"
          class="trending-chip"
          @click="searchTopic(topic.name)"
        />

        <!-- Empty State -->
        <div v-if="trendingTopics.length === 0" class="empty-state">
          <q-icon name="tag" size="md" color="grey-5" />
          <p>No trending topics</p>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="quick-stats-section">
      <div class="section-header">
        <h6 class="section-title">
          <q-icon name="analytics" class="section-icon" />
          Today's Activity
        </h6>
      </div>

      <div class="stats-list">
        <div class="stat-row">
          <div class="stat-info">
            <q-icon name="post_add" color="blue" />
            <span>New Posts</span>
          </div>
          <span class="stat-value">{{ todayStats.posts }}</span>
        </div>

        <div class="stat-row">
          <div class="stat-info">
            <q-icon name="person_add" color="green" />
            <span>New Members</span>
          </div>
          <span class="stat-value">{{ todayStats.members }}</span>
        </div>

        <div class="stat-row">
          <div class="stat-info">
            <q-icon name="event" color="orange" />
            <span>Events Today</span>
          </div>
          <span class="stat-value">{{ todayStats.events }}</span>
        </div>

        <div class="stat-row">
          <div class="stat-info">
            <q-icon name="forum" color="purple" />
            <span>Active Discussions</span>
          </div>
          <span class="stat-value">{{ todayStats.discussions }}</span>
        </div>
      </div>
    </div>

    <!-- Suggested Connections -->
    <div class="suggestions-section">
      <div class="section-header">
        <h6 class="section-title">
          <q-icon name="people_outline" class="section-icon" />
          Suggested Connections
        </h6>
      </div>

      <div class="suggestions-list">
        <div
          v-for="suggestion in suggestedConnections"
          :key="suggestion.id"
          class="suggestion-card"
        >
          <UserAvatar 
            :user="suggestion" 
            :size="40"
            class="suggestion-avatar"
          />
          <div class="suggestion-info">
            <h6 class="suggestion-name">{{ suggestion.full_name }}</h6>
            <p class="suggestion-role">{{ suggestion.profile_type }}</p>
            <q-btn
              dense
              unelevated
              color="primary"
              label="Connect"
              size="sm"
              @click="sendConnectionRequest(suggestion.id)"
              class="connect-btn"
            />
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="suggestedConnections.length === 0" class="empty-state">
          <q-icon name="people" size="md" color="grey-5" />
          <p>No suggestions available</p>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="sidebar-footer">
      <q-btn
        flat
        dense
        label="Community Guidelines"
        @click="openGuidelines"
        class="footer-link"
      />
      <q-btn
        flat
        dense
        label="Help & Support"
        @click="openSupport"
        class="footer-link"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { feedDataService } from '../../services/feedDataService'
import { useConnectionsService } from '../../services/connectionsService'
import UserAvatar from '../common/UserAvatar.vue'

// Composables
const router = useRouter()
// feedDataService is imported as singleton
const connectionsService = useConnectionsService()

// State
const announcements = ref([])
const trendingTopics = ref([])
const suggestedConnections = ref([])
const todayStats = ref({
  posts: 0,
  members: 0,
  events: 0,
  discussions: 0
})

// Methods
function formatDate(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) return 'Today'
  if (diffDays === 2) return 'Yesterday'
  if (diffDays <= 7) return `${diffDays} days ago`
  
  return date.toLocaleDateString()
}

function openLink(url: string) {
  window.open(url, '_blank')
}

function searchTopic(topic: string) {
  router.push({
    path: '/newvirtual-community',
    query: { tab: 'feed', search: `#${topic}` }
  })
}

async function sendConnectionRequest(userId: string) {
  try {
    await connectionsService.sendConnectionRequest(userId)
    // Remove from suggestions after sending request
    suggestedConnections.value = suggestedConnections.value.filter(
      s => s.id !== userId
    )
  } catch (error) {
    console.error('Failed to send connection request:', error)
  }
}

function openGuidelines() {
  router.push('/community-guidelines')
}

function openSupport() {
  router.push('/support')
}

async function refreshAnnouncements() {
  await loadAnnouncements()
}

async function loadAnnouncements() {
  try {
    const data = await feedDataService.getCommunityAnnouncements()
    announcements.value = data || []
  } catch (error) {
    console.error('Failed to load announcements:', error)
    announcements.value = []
  }
}

async function loadTrendingTopics() {
  try {
    const data = await feedDataService.getTrendingTopics()
    trendingTopics.value = data || []
  } catch (error) {
    console.error('Failed to load trending topics:', error)
    trendingTopics.value = []
  }
}

async function loadSuggestedConnections() {
  try {
    const data = await connectionsService.getSuggestedConnections()
    suggestedConnections.value = data || []
  } catch (error) {
    console.error('Failed to load suggested connections:', error)
    suggestedConnections.value = []
  }
}

async function loadTodayStats() {
  try {
    const data = await feedDataService.getTodayStats()
    todayStats.value = data || {
      posts: 0,
      members: 0,
      events: 0,
      discussions: 0
    }
  } catch (error) {
    console.error('Failed to load today stats:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadAnnouncements(),
    loadTrendingTopics(),
    loadSuggestedConnections(),
    loadTodayStats()
  ])
})
</script>

<style scoped>
.virtual-community-right-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 16px;
  background: white;
  overflow-y: auto;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #111827;
}

.section-icon {
  font-size: 16px;
  color: #6366f1;
}

.refresh-btn {
  color: #6b7280;
  
  &:hover {
    color: #6366f1;
  }
}

/* Announcements */
.announcement-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  border-left: 3px solid #6366f1;
}

.announcement-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.announcement-icon {
  font-size: 16px;
}

.announcement-date {
  font-size: 11px;
  color: #6b7280;
}

.announcement-title {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #111827;
}

.announcement-content {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #4b5563;
  line-height: 1.4;
}

.announcement-link {
  font-size: 11px;
}

/* Trending Topics */
.trending-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.trending-chip {
  font-size: 11px;
  height: 24px;
}

/* Quick Stats */
.stats-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f9fafb;
  border-radius: 6px;
}

.stat-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #4b5563;
}

.stat-value {
  font-weight: 600;
  color: #111827;
  font-size: 14px;
}

/* Suggestions */
.suggestion-card {
  display: flex;
  gap: 12px;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  margin-bottom: 12px;
}

.suggestion-info {
  flex: 1;
}

.suggestion-name {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 600;
  color: #111827;
}

.suggestion-role {
  margin: 0 0 8px 0;
  font-size: 11px;
  color: #6b7280;
}

.connect-btn {
  font-size: 11px;
  height: 24px;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  
  p {
    margin: 8px 0 0 0;
    font-size: 12px;
  }
}

/* Footer */
.sidebar-footer {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.footer-link {
  font-size: 11px;
  color: #6b7280;
  justify-content: flex-start;
  
  &:hover {
    color: #6366f1;
  }
}
</style>
